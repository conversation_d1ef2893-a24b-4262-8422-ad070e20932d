import os, sys, logging
from colorlog import ColoredFormatter
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from datetime import datetime
from scraper.rental_scraper import is_allowed_to_scrape, scrape_rental_offer, get_rental_search_url, log_rental_scraping_summary
from scraper.rental_fetch_and_parse import download_rental_data_from_search_results, download_rental_data_page_by_page, check_if_rental_offer_exists, check_if_rental_price_changed, find_closed_rental_offers
from db.db_setup import create_tables
from db.rental_db_operations import insert_new_rental_listing, update_active_rental_offers, update_deleted_rental_offers
from db.db_setup import get_db_connection

from config.logging_config import setup_logger
logger = setup_logger()

# ZASADY: WYSZUKIWANIE MIESZKAN NA WYNAJEM W DANYM MIESCIE BEZ ZADNYCH FILTROW, ZALECANE SORTOWANIE OD NAJNOWSZYCH I MAX LIMIT OFERT NA STRONE

# Przykładowe URL dla wynajmu mieszkań w różnych miastach
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/warszawa/warszawa?viewType=listing&by=LATEST&direction=DESC&limit=72"
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/slaskie/katowice/katowice/katowice?viewType=listing&by=LATEST&direction=DESC&limit=72"

# Konfiguracja dla wynajmu
city = 'warszawa'
voivodeship = 'mazowieckie'
url = get_rental_search_url(city, voivodeship)
url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/pruszkowski?limit=36&by=DEFAULT&direction=DESC&viewType=map&mapBounds=21.148011777773732%2C52.2565355792791%2C20.472009253704826%2C51.94462300422175"
def main():
    conn = None
    cur = None
    
    # Liczniki dla podsumowania
    total_offers_processed = 0
    new_offers_added = 0
    updated_offers_count = 0
    closed_offers_count = 0
    
    try:
        conn = get_db_connection()
        if conn is None:
            logging.critical("Connection to the database failed")
            return
        cur = conn.cursor()
        
        # Upewnij się, że to dozwolone
        result = is_allowed_to_scrape(url)
        logging.warning(f"Is fetching rental page {url} allowed?: {result}")

        # Utwórz tabele jeżeli nie istnieją
        create_tables(cur)

        # Pobierz dane o ofertach wynajmu STRONA PO STRONIE
        logging.info("🚀 Rozpoczynam pobieranie i przetwarzanie ofert wynajmu STRONA PO STRONIE...")
        logging.info("💡 Każda strona będzie przetwarzana natychmiast po pobraniu!")

        # Zbierz wszystkie przetworzone oferty dla końcowego sprawdzenia usuniętych
        all_processed_offers = []

        # Przetwarzaj strony jedna po drugiej z natychmiastowym zapisem
        total_offers_processed = 0

        for page_num, total_pages, page_offers in download_rental_data_page_by_page(url):
            logging.info(f"\n{'='*80}")
            logging.info(f"📄 PRZETWARZAM STRONĘ {page_num}/{total_pages}")
            logging.info(f"🏠 Znaleziono {len(page_offers)} ofert na tej stronie")
            logging.info(f"💾 Każda oferta będzie zapisana NATYCHMIAST po przetworzeniu!")
            logging.info(f"{'='*80}")

            # Dodaj oferty z tej strony do listy wszystkich przetworzonych
            all_processed_offers.extend(page_offers)

            # Przetwarzaj każdą ofertę z bieżącej strony
            for i, offer in enumerate(page_offers, 1):
                total_offers_processed += 1
                id = offer.get("listing_id")

                if len(str(id)) != 8:
                    logging.debug(f"Pomijam ofertę {id} - nieprawidłowy format ID")
                    continue

                logging.info(f"🏠 [Strona {page_num}/{total_pages}] [{i}/{len(page_offers)}] Przetwarzam ofertę {id}...")

                # Jeżeli dana oferta nie znajduje się jeszcze w bazie, pobierz ją i zapisz
                try:
                    if not check_if_rental_offer_exists(offer, cur):
                        logging.info(f"🆕 [Strona {page_num}] [{i}/{len(page_offers)}] Oferta {id} nie istnieje w bazie - pobieranie pełnych danych...")
                        offer_data = scrape_rental_offer(offer)  # pobierz znalezioną ofertę w całości

                        if offer_data:  # jeżeli brak błędu wstaw dane do bazy
                            logging.info(f"💾 [Strona {page_num}] [{i}/{len(page_offers)}] ZAPISUJĘ ofertę {id} do bazy danych...")
                            id_db = insert_new_rental_listing(offer_data, conn, cur)  # wstaw do bazy (z automatycznym commit)

                            if id_db:
                                new_offers_added += 1
                                logging.info(f"✅ [Strona {page_num}] [{i}/{len(page_offers)}] Oferta {id} ZAPISANA w bazie pod ID {id_db}")
                                logging.info(f"   📝 Tytuł: {offer_data.get('title', 'N/A')[:50]}...")
                                logging.info(f"   💰 Cena: {offer_data.get('rental_price', 'N/A')} PLN")
                                logging.info(f"   📍 Lokalizacja: {offer_data.get('city', 'N/A')}, {offer_data.get('street', 'N/A')}")
                            else:
                                logging.warning(f"❌ [Strona {page_num}] [{i}/{len(page_offers)}] Nie udało się zapisać oferty {id} w bazie.")
                        else:
                            logging.warning(f"❌ [Strona {page_num}] [{i}/{len(page_offers)}] Nie udało się pobrać pełnych danych oferty {id} – pomijam.")

                    else:
                        # Oferta istnieje - sprawdź zmiany ceny
                        logging.info(f"🔄 [Strona {page_num}] [{i}/{len(page_offers)}] Oferta {id} istnieje w bazie, sprawdzanie zmian ceny...")
                        price_check_result = check_if_rental_price_changed(offer, cur)

                        if price_check_result is not None:
                            id_db, new_price, new_price_per_m = price_check_result
                            # Jeżeli new_price to nie False tylko liczba tzn że cena się zmieniła - update bazy
                            if new_price is not False and new_price is not None:  # Indicates a price change was found
                                logging.info(f"💰 [Strona {page_num}] [{i}/{len(page_offers)}] AKTUALIZUJĘ cenę oferty {id} w bazie...")
                                update_active_rental_offers((id_db, new_price, new_price_per_m), conn, cur)  # z automatycznym commit
                                updated_offers_count += 1
                                logging.info(f"✅ [Strona {page_num}] [{i}/{len(page_offers)}] Cena oferty {id} ({id_db}) ZAKTUALIZOWANA")
                                logging.info(f"   💰 Nowa cena: {new_price} PLN, nowa cena za m2: {new_price_per_m} PLN/m2")
                            elif new_price is False:
                                logging.info(f"ℹ️ [Strona {page_num}] [{i}/{len(page_offers)}] Cena oferty {id} ({id_db}) nie zmieniła się.")
                            else:  # new_price is None, but price_check_result was not None
                                logging.warning(f"⚠️ [Strona {page_num}] [{i}/{len(page_offers)}] Nieoczekiwany wynik sprawdzania ceny dla oferty {id} ({id_db}): {price_check_result}")
                        else:
                            logging.error(f"❌ [Strona {page_num}] [{i}/{len(page_offers)}] Nie udało się sprawdzić zmiany ceny dla oferty {id}.")

                    # Potwierdzenie, że oferta została przetworzona
                    logging.info(f"✅ [Strona {page_num}] [{i}/{len(page_offers)}] Oferta {id} przetworzona pomyślnie")

                except Exception as offer_error:
                    logging.exception(f"❌ [Strona {page_num}] [{i}/{len(page_offers)}] Błąd podczas przetwarzania oferty {id}: {offer_error}")
                    # Rollback transaction and continue with next offer
                    try:
                        conn.rollback()
                        logging.info(f"🔄 [Strona {page_num}] [{i}/{len(page_offers)}] Transaction rolled back, kontynuuję z następną ofertą")
                    except Exception as rollback_error:
                        logging.error(f"[Strona {page_num}] [{i}/{len(page_offers)}] Error during rollback: {rollback_error}")
                    continue

            # Podsumowanie po każdej stronie
            logging.info(f"\n📊 [Strona {page_num}/{total_pages}] ZAKOŃCZONA:")
            logging.info(f"   🏠 Oferty na stronie: {len(page_offers)}")
            logging.info(f"   ✅ Nowe oferty dodane: {new_offers_added}")
            logging.info(f"   🔄 Oferty zaktualizowane: {updated_offers_count}")
            logging.info(f"   📈 Łącznie przetworzonych: {total_offers_processed}")

            if page_num < total_pages:
                logging.info(f"➡️ Przechodzę do strony {page_num + 1}/{total_pages}...")
            else:
                logging.info(f"🎉 Wszystkie strony przetworzone!")



        # Podsumowanie przetwarzania ofert
        logging.info(f"\n{'='*80}")
        logging.info(f"PODSUMOWANIE PRZETWARZANIA OFERT WYNAJMU:")
        logging.info(f"✅ Nowe oferty dodane do bazy: {new_offers_added}")
        logging.info(f"🔄 Oferty zaktualizowane (zmiana ceny): {updated_offers_count}")
        logging.info(f"📊 Łącznie przetworzonych ofert: {total_offers_processed}")
        logging.info(f"{'='*80}\n")

        # Na końcu sprawdź, czy są jakieś usunięte oferty wynajmu
        logging.info("Rozpoczynam sprawdzanie czy jakieś oferty wynajmu nie zostały usunięte z otodom...")
        deleted_rental_offers = find_closed_rental_offers(all_processed_offers, city, cur)
        closed_offers_count = len(deleted_rental_offers)

        if closed_offers_count > 0:
            logging.info(f"Znaleziono {closed_offers_count} usuniętych ofert - aktualizuję w bazie...")
            for i, deleted_offer in enumerate(deleted_rental_offers, 1):
                logging.info(f"[{i}/{closed_offers_count}] Oznaczam ofertę {deleted_offer[1]} jako nieaktywną...")
                update_deleted_rental_offers(deleted_offer, conn, cur)  # z automatycznym commit
                logging.info(f"✅ [{i}/{closed_offers_count}] Oferta {deleted_offer[1]} oznaczona jako nieaktywna w bazie")
        else:
            logging.info("Nie znaleziono usuniętych ofert.")

        # Wyświetl końcowe podsumowanie
        log_rental_scraping_summary(total_offers_processed, new_offers_added, updated_offers_count, closed_offers_count)

        logging.info(f"\n🎉 Zakończono scraping ofert wynajmu - wszystkie dane zapisane w bazie danych!")
        logging.info(f"📈 Statystyki końcowe:")
        logging.info(f"   • Nowe oferty: {new_offers_added}")
        logging.info(f"   • Zaktualizowane oferty: {updated_offers_count}")
        logging.info(f"   • Zamknięte oferty: {closed_offers_count}")
        logging.info(f"   • Łącznie przetworzonych: {total_offers_processed}")
            
    except Exception as error:
        logging.exception("Error in main rental function:")
    finally: 
        if conn:
            if cur:  # Ensure cur is not None before closing
                cur.close()
            conn.close()


def main_with_custom_city(city_name: str, voivodeship_name: str | None = None, limit_offers: int | None = None):
    """
    Main function with custom city configuration for rental scraping.
    
    Args:
        city_name (str): Name of the city to scrape
        voivodeship_name (str, optional): Name of the voivodeship
        limit_offers (int, optional): Limit number of offers to process (for testing)
    """
    global city, voivodeship, url
    
    city = city_name.lower()
    voivodeship = voivodeship_name.lower() if voivodeship_name else None
    url = get_rental_search_url(city, voivodeship)
    
    logging.info(f"Rozpoczynam scraping ofert wynajmu dla miasta: {city_name}")
    logging.info(f"URL wyszukiwania: {url}")
    
    if limit_offers:
        logging.info(f"Ograniczenie liczby ofert do: {limit_offers}")
    
    main()


def test_rental_scraping():
    """
    Test function for rental scraping with minimal data.
    """
    logging.info("Rozpoczynam test scrapingu ofert wynajmu...")
    
    # Test z ograniczoną liczbą ofert
    main_with_custom_city("warszawa", "mazowieckie", limit_offers=3)
    
    logging.info("Test scrapingu ofert wynajmu zakończony.")


if __name__ == "__main__": 
    # Możesz uruchomić różne tryby:
    
    # 1. Standardowy scraping dla Warszawy
    main()
    
    # 2. Scraping dla innego miasta
    # main_with_custom_city("kraków", "małopolskie")
    
    # 3. Test z ograniczoną liczbą ofert
    # test_rental_scraping()
