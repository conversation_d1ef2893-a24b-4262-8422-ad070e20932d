import os
import psycopg2
import litellm
import csv
import logging
import threading
import concurrent.futures
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
PG_HOST = os.getenv("PG_HOST")
PG_PORT = os.getenv("PG_PORT", "5432")
PG_DATABASE = os.getenv("PG_DATABASE")
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
MAX_ANALYZER_THREADS = int(os.getenv("MAX_ANALYZER_THREADS", "15"))

# --- Locks for thread safety ---
csv_lock = threading.Lock()
processed_file_lock = threading.Lock()

# --- Constants ---
BASE_DIR = "rental_analyzer"
PROCESSED_LISTINGS_FILE = os.path.join(BASE_DIR, "processed_listings_llm.txt")
OUTPUT_CSV_FILE = os.path.join(BASE_DIR, "short_term_rental_analysis.csv")
LLM_MODEL = "gemini/gemini-2.5-flash-preview-05-20"
LLM_PROMPT = "Вы — ИИ-ассистент, специализирующийся на анализе польских объявлений об аренде недвижимости. Ваша задача — определить, предлагает ли объявление вариант краткосрочной аренды, под которой понимается любой срок аренды менее одного года. Анализируйте предоставленный текст объявления на основе следующих правил: Определите положительные индикаторы (краткосрочная аренда): Ищите явные ключевые слова и фразы, такие как: «Гибкий срок аренды» (Elastyczny okres najmu) «Краткосрочный» (Krótkoterminowy) Минимальный срок менее 12 месяцев (например, minimum 3 miesiące, od 6 miesięcy) Аренда до определенной даты (например, do czerwca, do 30.09) «Срок по договоренности» (Okres do uzgodnienia) Упоминание разных цен для разной продолжительности аренды. Определите отрицательные индикаторы (не краткосрочная аренда): Явное упоминание минимального годового контракта (например, minimum rok, umowa na 12 miesięcy). Термин najem okazjonalny (оказиональный/ситуационный наем) следует рассматривать как стандартную, долгосрочную аренду, А НЕ как вариант краткосрочной аренды. Если срок аренды вообще не упоминается, считайте, что это стандартная годовая аренда. Требуемый формат вывода: Если объявление предлагает краткосрочную аренду: Предоставьте краткое объяснение в одном предложении, цитируя или перефразируя ключевое доказательство из текста. Если объявление не предлагает краткосрочную аренду (требуется годовой контракт или информация отсутствует): Вы должны вернуть только слово False."

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Helper Functions ---

def ensure_directory_exists(directory_path):
    """Ensures that the specified directory exists."""
    if not os.path.exists(directory_path):
        try:
            os.makedirs(directory_path)
            logger.info(f"Created directory: {directory_path}")
        except OSError as e:
            logger.error(f"Error creating directory {directory_path}: {e}")
            raise

def load_processed_listings(file_path: str) -> set:
    """Loads processed listing IDs from a file."""
    ensure_directory_exists(os.path.dirname(file_path))
    processed_ids = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r') as f:
                processed_ids = {line.strip() for line in f if line.strip()}
        except IOError as e:
            logger.error(f"Error reading processed listings file {file_path}: {e}")
    return processed_ids

def add_to_processed_listings(file_path: str, listing_id: str):
    """Adds a listing ID to the processed listings file."""
    ensure_directory_exists(os.path.dirname(file_path))
    with processed_file_lock:
        try:
            with open(file_path, 'a') as f:
                f.write(f"{listing_id}\n")
        except IOError as e:
            logger.error(f"Error writing to processed listings file {file_path}: {e}")

def connect_db():
    """Connects to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            database=os.getenv("DB_NAME"),
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD")
        )
        logger.info("Successfully connected to the PostgreSQL database.")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        return None

def fetch_listings(conn) -> list:
    """Fetches listings from the apartments_rental_listings table."""
    listings = []
    if not conn:
        return listings
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT otodom_listing_id, offer_link, description_text, title, advert_type,
                       creation_date, pushed_ap_at, creation_source, area, rental_price,
                       updated_rental_price, rental_price_per_m, updated_rental_price_per_m,
                       deposit_amount, additional_rent, rooms_num, floor_num,
                       building_build_year, owner_id, owner_name
                FROM apartments_rental_listings
                WHERE description_text IS NOT NULL AND description_text <> '';
            """)
            listings = cur.fetchall()
            logger.info(f"Fetched {len(listings)} listings from the database.")
    except psycopg2.Error as e:
        logger.error(f"Error fetching listings from database: {e}")
    return listings

def analyze_description_with_llm(description: str) -> str | None:
    """Analyzes listing description using LLM to check for short-term rental mentions."""
    if not GOOGLE_API_KEY:
        logger.error("GOOGLE_API_KEY not set. Cannot call LLM.")
        return None
    
    litellm.api_key = GOOGLE_API_KEY # For Gemini, litellm expects api_key to be set

    messages = [
        {"role": "user", "content": f"{LLM_PROMPT}\n\nText: \"{description[:10000]}\""} # Limit description length if necessary
    ]
    try:
        response = litellm.completion(
            model=LLM_MODEL,
            messages=messages,
            stream=False  # Ensure non-streaming response
        )
        logger.debug(f"LLM response object type: {type(response)}")
        # logger.debug(f"LLM response object dir: {dir(response)}") # Uncomment for more detailed debugging
        # logger.debug(f"LLM full response: {response}") # Uncomment for more detailed debugging

        llm_response_content = response.choices[0].message.content.strip().lower()
        logger.debug(f"LLM extracted content: {llm_response_content}")
        return llm_response_content
        # if "true" in llm_response_content:
        #     return True
        # elif "false" in llm_response_content:
        #     return False
        # else:
        #     logger.warning(f"LLM response not clearly True/False: '{llm_response_content}'")
        #     return None
    except Exception as e:
        logger.error(f"Error calling LLM: {e}")
        return None

def append_to_csv(file_path: str, data_row: list):
    """Appends a data row to the CSV file."""
    ensure_directory_exists(os.path.dirname(file_path))
    with csv_lock:
        try:
            # Re-check file_exists inside the lock if header writing depends on atomicity with first write
            # For simplicity, assuming initial check is sufficient or header is idempotent if written multiple times by mistake (though unlikely with proper main logic)
            # A more robust way for header: check if file is empty right before writing.
            is_new_file = not os.path.exists(file_path) or os.path.getsize(file_path) == 0

            with open(file_path, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                if is_new_file: # Write header only if file is new or empty
                    writer.writerow([
                        'listing_id', 'listing_url', 'is_short_term_rent_mentioned', 'title', 'advert_type',
                        'creation_date', 'pushed_ap_at', 'creation_source', 'area', 'rental_price',
                        'updated_rental_price', 'rental_price_per_m', 'updated_rental_price_per_m',
                        'deposit_amount', 'additional_rent', 'rooms_num', 'floor_num',
                        'building_build_year', 'owner_id', 'owner_name'
                    ])
                writer.writerow(data_row)
        except IOError as e:
            logger.error(f"Error writing to CSV file {file_path}: {e}")

# --- Worker Function for Threading ---
def process_listing_worker(listing_data):
    """Worker function to process a single listing."""
    (listing_id, listing_url, description, title, advert_type, creation_date,
     pushed_ap_at, creation_source, area, rental_price, updated_rental_price,
     rental_price_per_m, updated_rental_price_per_m, deposit_amount, additional_rent,
     rooms_num, floor_num, building_build_year, owner_id, owner_name) = listing_data

    logger.info(f"Thread {threading.get_ident()}: Processing listing: {listing_id} - {listing_url}")

    if not description or not description.strip():
        logger.warning(f"Thread {threading.get_ident()}: Listing {listing_id} has no description. Skipping LLM analysis.")
        # Optionally, record this case in CSV if needed, e.g., with False or None
        # append_to_csv(OUTPUT_CSV_FILE, [listing_id, listing_url, False, title, advert_type, creation_date, pushed_ap_at, creation_source, area, rental_price, updated_rental_price, rental_price_per_m, updated_rental_price_per_m, deposit_amount, additional_rent, rooms_num, floor_num, building_build_year, owner_id, owner_name])
        # add_to_processed_listings(PROCESSED_LISTINGS_FILE, str(listing_id)) # Mark as processed even if skipped
        return

    is_short_term = analyze_description_with_llm(description)

    if is_short_term is not None:
        append_to_csv(OUTPUT_CSV_FILE, [
            listing_id, listing_url, is_short_term, title, advert_type, creation_date,
            pushed_ap_at, creation_source, area, rental_price, updated_rental_price,
            rental_price_per_m, updated_rental_price_per_m, deposit_amount, additional_rent,
            rooms_num, floor_num, building_build_year, owner_id, owner_name
        ])
        add_to_processed_listings(PROCESSED_LISTINGS_FILE, str(listing_id))
        logger.info(f"Thread {threading.get_ident()}: Successfully processed and recorded listing: {listing_id}. Short-term mention: {is_short_term}")
    else:
        logger.warning(f"Thread {threading.get_ident()}: Failed to get a clear LLM response for listing: {listing_id}. It will not be marked as processed.")

# --- Main Function ---
def main():
    """Main function to orchestrate the rental analysis."""
    logger.info("Starting rental analysis script.")
    
    ensure_directory_exists(BASE_DIR)

    processed_listing_ids = load_processed_listings(PROCESSED_LISTINGS_FILE)
    logger.info(f"Loaded {len(processed_listing_ids)} processed listing IDs.")

    db_conn = connect_db()
    if not db_conn:
        logger.error("Exiting due to database connection failure.")
        return

    all_listings_from_db = fetch_listings(db_conn)
    
    if db_conn: # Close connection after fetching, as workers won't use it.
        db_conn.close()
        logger.info("Database connection closed.")

    if not all_listings_from_db:
        logger.info("No listings fetched from database.")
        logger.info("Rental analysis script finished.")
        return

    # Filter out already processed listings before submitting to threads
    listings_to_actually_process = []
    for listing_data in all_listings_from_db:
        listing_id = listing_data[0]  # otodom_listing_id is the first field
        if str(listing_id) not in processed_listing_ids:
            listings_to_actually_process.append(listing_data)
        else:
            logger.info(f"Skipping already processed listing (pre-thread): {listing_id}")
            
    if not listings_to_actually_process:
        logger.info("No new listings to process.")
        logger.info("Rental analysis script finished.")
        return
        
    logger.info(f"Submitting {len(listings_to_actually_process)} listings to {MAX_ANALYZER_THREADS} worker threads.")

    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_ANALYZER_THREADS) as executor:
        # Submit tasks to the executor
        future_to_listing = {executor.submit(process_listing_worker, listing_data): listing_data for listing_data in listings_to_actually_process}
        
        for future in concurrent.futures.as_completed(future_to_listing):
            listing_data = future_to_listing[future]
            try:
                future.result()  # You can get results here if worker returned something, or catch exceptions
            except Exception as exc:
                logger.error(f"Thread {threading.get_ident()}: Listing {listing_data[0]} generated an exception: {exc}")
    
    logger.info("Rental analysis script finished.")

if __name__ == "__main__":
    # Ensure GOOGLE_API_KEY is available for litellm
    if not GOOGLE_API_KEY:
        logger.critical("GOOGLE_API_KEY environment variable is not set. LLM calls will fail.")
        # Decide if to exit or proceed without LLM
        # exit(1) 
    
    # For litellm, setting the API key globally or per call.
    # If using a specific provider like 'gemini', litellm might handle it via GOOGLE_API_KEY.
    # litellm.gemini_api_key = GOOGLE_API_KEY # if litellm requires specific provider key setting

    main()