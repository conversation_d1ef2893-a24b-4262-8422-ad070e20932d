#!/usr/bin/env python3
"""
Test script to verify that the main scraper now uses enhanced data extraction.

This script:
1. Tests the scrape_rental_offer function used by main_rental.py
2. Verifies that it extracts comprehensive data (not just basic fields)
3. Confirms that future runs of main_rental.py will get full data

Usage:
    python test_main_scraper_enhanced.py
"""

import logging
from scraper.rental_scraper import scrape_rental_offer
from config.logging_config import setup_logger

# Setup logging
logger = setup_logger()

def test_main_scraper_enhanced_extraction():
    """Test that the main scraper now extracts comprehensive data"""
    
    print("="*80)
    print("TESTING MAIN SCRAPER ENHANCED DATA EXTRACTION")
    print("="*80)
    
    # Test with a sample offer (same format as main_rental.py uses)
    test_offer = {
        "link": "https://www.otodom.pl/pl/oferta/mieszkanie-53-m2-dwa-pokoje-ul-bobrowiecka-3a-bezposrednio-ID4vJsR",
        "listing_id": "66906583"
    }
    
    print(f"🔄 Testing scrape_rental_offer() function used by main_rental.py...")
    print(f"📋 Test URL: {test_offer['link']}")
    print(f"📋 Test ID: {test_offer['listing_id']}")
    
    # Call the same function that main_rental.py calls
    result = scrape_rental_offer(test_offer)
    
    if result is None:
        print("❌ FAILED: scrape_rental_offer returned None")
        return False
    
    print(f"\n✅ SUCCESS: scrape_rental_offer returned data")
    print(f"📊 Total fields extracted: {len(result)}")
    
    # Check for key fields that should be extracted with enhanced logic
    key_fields_to_check = [
        'area', 'rental_price', 'rental_price_per_m', 'rooms_num', 'floor_num',
        'heating', 'building_build_year', 'building_material', 'building_type',
        'windows_type', 'construction_status', 'deposit_amount'
    ]
    
    print(f"\n📋 CHECKING KEY FIELDS:")
    print("-" * 60)
    
    extracted_fields = 0
    missing_fields = 0
    
    for field in key_fields_to_check:
        value = result.get(field)
        if value is not None and value != '':
            print(f"✅ {field:<25} = {value}")
            extracted_fields += 1
        else:
            print(f"❌ {field:<25} = NULL/EMPTY")
            missing_fields += 1
    
    print("-" * 60)
    print(f"📊 SUMMARY:")
    print(f"   ✅ Extracted fields: {extracted_fields}/{len(key_fields_to_check)}")
    print(f"   ❌ Missing fields:   {missing_fields}/{len(key_fields_to_check)}")
    print(f"   📈 Success rate:     {(extracted_fields/len(key_fields_to_check)*100):.1f}%")
    
    # Check if we're getting comprehensive data (at least 8 out of 12 key fields)
    if extracted_fields >= 8:
        print(f"\n🎉 EXCELLENT! Main scraper is now extracting comprehensive data!")
        print(f"✅ Future runs of main_rental.py will extract {extracted_fields}+ fields per listing")
        print(f"✅ No need to run reprocessing scripts again for new listings")
        return True
    elif extracted_fields >= 5:
        print(f"\n⚠️ GOOD: Main scraper is extracting more data than before")
        print(f"✅ Future runs will be better, but some fields might still be missing")
        return True
    else:
        print(f"\n❌ PROBLEM: Main scraper is still not extracting enough data")
        print(f"❌ You may need to run reprocessing scripts again for new listings")
        return False

def test_specific_enhanced_features():
    """Test specific enhanced features that were added"""
    
    print(f"\n" + "="*80)
    print("TESTING SPECIFIC ENHANCED FEATURES")
    print("="*80)
    
    test_offer = {
        "link": "https://www.otodom.pl/pl/oferta/3-pokojowe-dwustronne-osiedle-lopuszanska-47-ID4vJsu",
        "listing_id": "66906586"
    }
    
    result = scrape_rental_offer(test_offer)
    
    if result is None:
        print("❌ FAILED: Could not extract data")
        return False
    
    # Test enhanced features specifically
    enhanced_features = {
        'target.Area extraction': result.get('area'),
        'Price per m² calculation': result.get('rental_price_per_m'),
        'Floor number extraction': result.get('floor_num'),
        'Deposit amount': result.get('deposit_amount'),
        'Building year': result.get('building_build_year'),
        'Heating type': result.get('heating'),
        'Building material': result.get('building_material'),
        'Windows type': result.get('windows_type'),
        'Energy certificate': result.get('energy_certificate')
    }
    
    print("🔍 ENHANCED FEATURES TEST:")
    print("-" * 60)
    
    working_features = 0
    total_features = len(enhanced_features)
    
    for feature_name, value in enhanced_features.items():
        if value is not None and value != '':
            print(f"✅ {feature_name:<30} = {value}")
            working_features += 1
        else:
            print(f"❌ {feature_name:<30} = NULL/EMPTY")
    
    print("-" * 60)
    print(f"📊 Enhanced features working: {working_features}/{total_features} ({working_features/total_features*100:.1f}%)")
    
    if working_features >= 6:
        print(f"🎉 EXCELLENT! Enhanced extraction is working perfectly!")
        return True
    elif working_features >= 4:
        print(f"✅ GOOD! Most enhanced features are working!")
        return True
    else:
        print(f"⚠️ Some enhanced features may not be working as expected")
        return False

def main():
    """Main test function"""
    
    print("🧪 TESTING MAIN SCRAPER ENHANCED DATA EXTRACTION")
    print("🎯 Goal: Verify that main_rental.py will now extract comprehensive data")
    print("🔧 Testing the same functions that main_rental.py uses...")
    
    try:
        # Test 1: Basic enhanced extraction
        test1_result = test_main_scraper_enhanced_extraction()
        
        # Test 2: Specific enhanced features
        test2_result = test_specific_enhanced_features()
        
        # Final summary
        print(f"\n" + "="*80)
        print("FINAL TEST RESULTS")
        print("="*80)
        
        if test1_result and test2_result:
            print("🎉 SUCCESS! Main scraper is now fully enhanced!")
            print("✅ Future runs of main_rental.py will extract comprehensive data")
            print("✅ No need to reprocess new listings - they'll have full data")
            print("🚀 You can run main_rental.py with confidence!")
        elif test1_result or test2_result:
            print("⚠️ PARTIAL SUCCESS! Main scraper is improved but not perfect")
            print("✅ Future runs will be better than before")
            print("⚠️ Some manual reprocessing might still be needed")
        else:
            print("❌ PROBLEM! Main scraper enhancement may not be working")
            print("❌ You may need to check the implementation")
        
        print("="*80)
        
    except Exception as error:
        logging.exception(f"❌ Error during testing: {error}")
        print(f"❌ Test failed with error: {error}")

if __name__ == "__main__":
    main()
